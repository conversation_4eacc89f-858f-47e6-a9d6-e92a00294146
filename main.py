import streamlit as st
from dashboard_tab import dashboard_page
from policy_selection_tab import policy_selection_page
from illustration_tab import illustration_page
from history_tab import history_page
from analysis_tab import analysis_page

# Page configuration
st.set_page_config(
    page_title="Insurance Policy Management System",
    page_icon="🏢",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'logged_in' not in st.session_state:
    st.session_state.logged_in = False
if 'scenarios_history' not in st.session_state:
    st.session_state.scenarios_history = []
if 'selected_scenarios' not in st.session_state:
    st.session_state.selected_scenarios = []

# Hardcoded credentials
USERS = {
    "admin": {"password": "admin123", "name": "Administrator"},
    "user1": {"password": "user123", "name": "<PERSON>"},
    "demo": {"password": "demo123", "name": "Demo User"}
}

def login_page():
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown(
            """
            <h1 style='text-align: center; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5em; color: #4F8BF9;'>Welcome to Insurance Portal</h1>
            <hr style='border: 1px solid #e6e6e6; margin-bottom: 2em;'>
            """,
            unsafe_allow_html=True
        )
        with st.form("login_form"):
            username = st.text_input("Username", placeholder="Enter your username")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            show_password = st.checkbox("Show Password")
            if show_password:
                password = st.text_input("Password (visible)", value=password, key="visible_password")
            col_login, col_reset, col_forgot = st.columns(3)
            with col_login:
                login_button = st.form_submit_button("Login", use_container_width=True)
            with col_reset:
                reset_button = st.form_submit_button("Reset", use_container_width=True)
            with col_forgot:
                forgot_button = st.form_submit_button("Forgot Password", use_container_width=True)
        if login_button:
            if username in USERS and USERS[username]["password"] == password:
                st.session_state.logged_in = True
                st.session_state.username = username
                st.session_state.user_name = USERS[username]["name"]
                st.success("Login successful!")
                st.rerun()
            else:
                st.error("Invalid username or password!")
        if reset_button:
            st.info("Form reset!")
            st.rerun()
        if forgot_button:
            st.info("Please contact administrator for password reset.")
        st.markdown("---")
        st.markdown("**Demo Credentials:**")
        st.code("Username: demo\nPassword: demo123")

def main():
    if not st.session_state.logged_in:
        login_page()
        return
    # Sidebar navigation
    st.sidebar.title("Navigation")
    st.sidebar.markdown(f"**Welcome, {st.session_state.get('user_name', '')}**")
    pages = {
        "📊 Dashboard": dashboard_page,
        "📋 Policy Selection": policy_selection_page,
        "📊 Illustration": illustration_page,
        "📋 History": history_page,
        "📈 Analysis": analysis_page
    }
    if 'current_page' not in st.session_state:
        st.session_state.current_page = list(pages.keys())[0]
    st.sidebar.markdown("---")
    st.sidebar.markdown("#### Navigation")
    for page_name in pages.keys():
        if page_name == st.session_state.current_page:
            st.sidebar.markdown(f"<div style='background-color:#4F8BF9;padding:8px 16px;border-radius:6px;color:white;font-weight:bold;margin-bottom:4px;cursor:pointer;'>{page_name} ✅</div>", unsafe_allow_html=True)
        else:
            if st.sidebar.button(page_name, key=page_name):
                st.session_state.current_page = page_name
                st.session_state.scroll_to_top = True  # Trigger scroll to top
                st.rerun()
    st.sidebar.markdown("---")
    st.sidebar.markdown("<div style='height:40px'></div>", unsafe_allow_html=True)  # Spacer
    # Logout button
    if st.sidebar.button("\U0001F6AA Logout"):
        st.session_state.logged_in = False
        st.session_state.clear()
        st.rerun()
    # Handle scroll to top when navigating between pages
    if st.session_state.get('scroll_to_top', False):
        st.markdown("""
        <script>
            setTimeout(function() {
                // Multiple methods to ensure scrolling works
                window.parent.document.body.scrollTop = 0;
                window.parent.document.documentElement.scrollTop = 0;

                // Try to find and scroll the main container
                const containers = [
                    '.main',
                    '.stApp',
                    '[data-testid="stAppViewContainer"]',
                    '.css-1d391kg'
                ];

                containers.forEach(selector => {
                    const element = window.parent.document.querySelector(selector);
                    if (element) {
                        element.scrollTop = 0;
                    }
                });

                // Force scroll using window methods
                window.parent.scrollTo(0, 0);
            }, 100);
        </script>
        """, unsafe_allow_html=True)
        st.session_state.scroll_to_top = False  # Reset the flag

    # Display selected page
    pages[st.session_state.current_page]()

if __name__ == "__main__":
    main() 