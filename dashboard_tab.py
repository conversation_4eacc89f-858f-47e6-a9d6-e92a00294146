import streamlit as st
import pandas as pd
from datetime import datetime

# Enhanced customer data matching the image details
customer_data = {
    ("<PERSON>", "POL-12345678", "CUS-567890"): {
        "DOB": "05.02.1994",
        "Email": "<EMAIL>",
        "Phone": "(*************",
        "Customer ID": "CUS-567890",
        "Policy Number": "POL-12345678",
        "Policy Type": "Whole Life Insurance",
        "Status": "Active",
        "Start Date": "07/06/2021",
        "Maturity Date": "08/09/2065",
        "Face Amount": "500,000 $",
        "Premium Payment": "Annually",
        "Premium Amount": "2000 $",
        "Payment History": [
            ("07/06/2021", "2000 $"),
            ("06/06/2022", "2000 $"),
            ("06/06/2023", "2000 $"),
            ("05/06/2024", "2000 $")
        ]
    },
    # Add more sample customers for testing
    ("<PERSON> Doe", "POL-87654321", "CUS-123456"): {
        "DOB": "12.08.1988",
        "Email": "<EMAIL>",
        "Phone": "(*************",
        "Customer ID": "CUS-123456",
        "Policy Number": "POL-87654321",
        "Policy Type": "Term Life Insurance",
        "Status": "Active",
        "Start Date": "15/03/2020",
        "Maturity Date": "15/03/2050",
        "Face Amount": "750,000 $",
        "Premium Payment": "Monthly",
        "Premium Amount": "150 $",
        "Payment History": [
            ("15/03/2020", "150 $"),
            ("15/04/2020", "150 $"),
            ("15/05/2020", "150 $"),
            ("15/06/2020", "150 $")
        ]
    }
}

def dashboard_page():
    # Initialize session state for navigation
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'dashboard'
    
    # Simple and clean styling
    st.markdown("""
    <style>
        /* Hide Streamlit elements */
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        .stDeployButton {display: none;}
        header {visibility: hidden;}
        
        /* Page background */
        .stApp {
            background-color: #f5f7fa;
        }
        
        /* Main title */
        .main-title {
            text-align: center;
            font-size: 3rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* Section headers */
        .section-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #34495e;
            margin: 20px 0 15px 0;
            padding: 15px;
            background: #3498db;
            color: white;
            border-radius: 8px;
            text-align: center;
        }
        
        /* Input styling */
        .stTextInput input {
            border: 2px solid #3498db;
            border-radius: 5px;
            padding: 10px;
            font-size: 16px;
        }
        
        /* Detail cards */
        .detail-card {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }
        
        .detail-label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .detail-value {
            color: #34495e;
            font-size: 16px;
        }
        
        /* Payment history - Compact version */
        .payment-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 15px 0;
            overflow: hidden;
        }
        
        .payment-header {
            background: #3498db;
            color: white;
            padding: 12px 15px;
            font-weight: bold;
            font-size: 14px;
            margin: 0;
        }
        
        .payment-row {
            padding: 10px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            color: #2c3e50;
            font-size: 14px;
            font-weight: 500;
        }
        
        .payment-row:last-child {
            border-bottom: none;
        }
        
        .payment-date {
            color: #2c3e50;
            font-weight: bold;
        }
        
        .payment-amount {
            color: #27ae60;
            font-weight: bold;
        }
        
        /* Status badge */
        .status-active {
            background: #27ae60;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-weight: bold;
        }
        
        /* Get Started Button Styling */
        .get-started-container {
            display: flex;
            justify-content: center;
            margin-top: 40px;
            margin-bottom: 20px;
            padding: 20px;
        }

        .stButton > button {
            background: linear-gradient(45deg, #3498db, #2980b9) !important;
            color: white !important;
            border: none !important;
            padding: 15px 40px !important;
            border-radius: 8px !important;
            font-size: 18px !important;
            font-weight: bold !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3) !important;
        }

        .stButton > button:hover {
            background: linear-gradient(45deg, #2980b9, #1f5f8b) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4) !important;
        }

        .stButton > button:active {
            transform: translateY(0px) !important;
        }
        
        /* Alert styling */
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #27ae60;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #e74c3c;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #3498db;
        }
        
        /* Policy selection styles */
        .policy-card {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .policy-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }
        
        .policy-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .policy-description {
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        
        .policy-features {
            font-size: 14px;
            color: #34495e;
        }
    </style>
    """, unsafe_allow_html=True)

    # Main Title - Now clearly visible
    st.markdown("<h1 class='main-title'>AIETON LABS</h1>", unsafe_allow_html=True)
    
    # Customer Details Section
    st.markdown("<div class='section-title'>Customer Lookup</div>", unsafe_allow_html=True)

    # Input columns for Customer Name, Policy Number, and Customer ID
    col1, col2, col3 = st.columns(3)
    
    with col1:
        customer_name = st.text_input("Customer Name", placeholder="Enter full name")
    with col2:
        policy_number = st.text_input("Policy Number", placeholder="POL-12345678")
    with col3:
        customer_id = st.text_input("Customer ID", placeholder="CUS-567890")

    # Check if all fields are filled
    if customer_name and policy_number and customer_id:
        # Create lookup key
        lookup_key = (customer_name.strip(), policy_number.strip(), customer_id.strip())
        
        # Check if details match
        if lookup_key in customer_data:
            details = customer_data[lookup_key]
            
            # Success message
            st.markdown("<div class='alert alert-success'>✅ Customer found! Details loaded successfully.</div>", unsafe_allow_html=True)
            
            # Personal Information
            st.markdown("<div class='section-title'>Personal Information</div>", unsafe_allow_html=True)
            
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Customer Name</div>
                    <div class='detail-value'>{customer_name}</div>
                </div>
                """, unsafe_allow_html=True)
                
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Date of Birth</div>
                    <div class='detail-value'>{details['DOB']}</div>
                </div>
                """, unsafe_allow_html=True)
                
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Email</div>
                    <div class='detail-value'>{details['Email']}</div>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Phone</div>
                    <div class='detail-value'>{details['Phone']}</div>
                </div>
                """, unsafe_allow_html=True)
                
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Customer ID</div>
                    <div class='detail-value'>{details['Customer ID']}</div>
                </div>
                """, unsafe_allow_html=True)
            
            # Policy Details
            st.markdown("<div class='section-title'>Policy Details</div>", unsafe_allow_html=True)
            
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Policy Number</div>
                    <div class='detail-value'>{details['Policy Number']}</div>
                </div>
                """, unsafe_allow_html=True)
                
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Policy Type</div>
                    <div class='detail-value'>{details['Policy Type']}</div>
                </div>
                """, unsafe_allow_html=True)
                
                status_badge = "status-active" if details['Status'] == "Active" else "status-inactive"
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Status</div>
                    <div class='detail-value'><span class='{status_badge}'>{details['Status']}</span></div>
                </div>
                """, unsafe_allow_html=True)
                
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Start Date</div>
                    <div class='detail-value'>{details['Start Date']}</div>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Maturity Date</div>
                    <div class='detail-value'>{details['Maturity Date']}</div>
                </div>
                """, unsafe_allow_html=True)
                
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Face Amount</div>
                    <div class='detail-value'>{details['Face Amount']}</div>
                </div>
                """, unsafe_allow_html=True)
                
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Premium Payment</div>
                    <div class='detail-value'>{details['Premium Payment']}</div>
                </div>
                """, unsafe_allow_html=True)
                
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>Premium Amount</div>
                    <div class='detail-value'>{details['Premium Amount']}</div>
                </div>
                """, unsafe_allow_html=True)
            
            # Payment History
            st.markdown("<div class='section-title'>Payment History</div>", unsafe_allow_html=True)
            
            # Compact payment table
            st.markdown("""
            <div class='payment-table'>
                <div class='payment-header'>
                    <div style='display: flex; justify-content: space-between; align-items: center;'>
                        <span>Payment Date</span>
                        <span>Amount</span>
                    </div>
                </div>
            """, unsafe_allow_html=True)
            
            # Payment rows
            for date, amount in details["Payment History"]:
                st.markdown(f"""
                <div class='payment-row'>
                    <div style='display: flex; justify-content: space-between; align-items: center;'>
                        <span class='payment-date'>{date}</span>
                        <span class='payment-amount'>{amount}</span>
                    </div>
                </div>
                """, unsafe_allow_html=True)
            
            st.markdown("</div>", unsafe_allow_html=True)
            
            # Centered Get Started Button at the bottom
            st.markdown("""
            <div style='display: flex; justify-content: center; margin-top: 40px; margin-bottom: 20px;'>
            """, unsafe_allow_html=True)
            center_col = st.columns([1,2,1])[1]
            with center_col:
                if st.button("Get Started", key="get_started", help="Proceed to policy selection", type="primary", use_container_width=True):
                    st.session_state.current_page = '📋 Policy Selection'
                    st.rerun()
            st.markdown("""
            </div>
            """, unsafe_allow_html=True)
            
        else:
            st.markdown("<div class='alert alert-error'>❌ Customer not found. Please check your details.</div>", unsafe_allow_html=True)
            st.markdown("""
            <div class='alert alert-info'>
                <strong>Sample Test Data:</strong><br>
                Customer Name: John Smith<br>
                Policy Number: POL-12345678<br>
                Customer ID: CUS-567890
            </div>
            """, unsafe_allow_html=True)
    
    elif customer_name or policy_number or customer_id:
        st.markdown("<div class='alert alert-info'>⚠️ Please fill in all three fields to search.</div>", unsafe_allow_html=True)
    
    else:
        st.markdown("<div class='alert alert-info'>ℹ️ Enter your credentials to view policy details.</div>", unsafe_allow_html=True)
        
        # Show sample data
        with st.expander("View Sample Customer Data"):
            for key, value in customer_data.items():
                st.write(f"**{key[0]}** - {key[1]} - {key[2]}")
                st.write(f"Policy Type: {value['Policy Type']}")
                st.write("---")

